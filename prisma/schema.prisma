datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

enum Language {
  EN
  VI
}

enum Provider {
  TELEGRAM
  USERNAME_PASSWORD
  GOOGLE
}

enum Role {
  USER
  ADMIN
}

enum PartsOfSpeech {
  NOUN
  VERB
  ADJECTIVE
  ADVERB
  PRONOUN
  PREPOSITION
  CONJUNCTION
  INTERJECTION
}

enum Difficulty {
  BEGINNER
  INTERMEDIATE
  ADVANCED
}

enum Length {
  SHORT
  MEDIUM
  LONG
}

enum AuditAction {
  CREATE
  UPDATE
  DELETE
  LOGIN
  LOGOUT
  ACCESS
  EXPORT
  IMPORT
  APPROVE
  REJECT
  ENABLE
  DISABLE
  CLEAR_CACHE
  SYSTEM_CHECK
}

model User {
  id               String            @id() @default(uuid())
  provider         Provider
  provider_id      String
  username         String?
  password_hash    String?
  role             Role              @default(USER)
  disabled         Boolean           @default(false)
  collections      Collection[]
  last_seen_words  LastSeenWord[]
  keywords         Keyword[]
  feedbacks        Feedback[]
  collection_stats CollectionStats[]
  audit_logs_user  AuditLog[]        @relation("AuditLogUser")
  audit_logs_admin AuditLog[]        @relation("AuditLogAdmin")
  created_at       DateTime          @default(now())
  updated_at       DateTime          @updatedAt()
  UserWordPackage  UserWordPackage[]

  @@unique([provider, provider_id])
  @@unique([username])
}

model Word {
  id            String       @id() @default(uuid())
  term          String
  language      Language
  audio_url     String?
  definitions   Definition[]
  created_at    DateTime     @default(now())
  updated_at    DateTime     @updatedAt()
  WordNetData   WordNetData? @relation(fields: [wordNetDataId], references: [id])
  wordNetDataId String?

  @@unique([term, language])
  @@index([term])
  @@index([language])
  @@index([term, language])
}

model Definition {
  id       String          @id() @default(uuid())
  word     Word            @relation(fields: [word_id], references: [id])
  word_id  String
  pos      PartsOfSpeech[]
  ipa      String
  images   String[]
  explains Explain[]
  examples Example[]
}

model Explain {
  id            String     @id() @default(uuid())
  EN            String
  VI            String
  definition    Definition @relation(fields: [definition_id], references: [id])
  definition_id String
}

model Example {
  id            String     @id() @default(uuid())
  EN            String
  VI            String
  definition    Definition @relation(fields: [definition_id], references: [id])
  definition_id String
}

model WordNetData {
  id         String   @id() @default(uuid())
  term       String // Từ gốc từ WordNet
  pos        String // Part of speech (noun, verb, adj, adv)
  synsets    String[] // Tập từ đồng nghĩa có cùng nghĩa
  lemma      String? // Dạng cơ bản của từ
  hypernyms  String[] // Nghĩa rộng hơn (cha)
  hyponyms   String[] // Nghĩa hẹp hơn (con)
  holonyms   String[] // Tập thể chứa nó
  meronyms   String[] // Thành phần của nó
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt()
  Word       Word[]

  @@unique([term, pos])
  @@index([term])
  @@index([lemma])
  @@index([pos])
}

model Keyword {
  id      String @id() @default(uuid())
  content String
  user    User   @relation(fields: [user_id], references: [id], onDelete: Cascade)
  user_id String

  @@index([user_id])
}

model Collection {
  id                             String            @id() @default(uuid())
  name                           String
  target_language                Language          @default(EN)
  source_language                Language          @default(VI)
  user                           User              @relation(fields: [user_id], references: [id], onDelete: Cascade)
  user_id                        String
  word_ids                       String[]
  paragraph_ids                  String[]
  keyword_ids                    String[]
  enable_learn_word_notification Boolean           @default(false)
  collection_stats               CollectionStats[]
  created_at                     DateTime          @default(now())
  updated_at                     DateTime          @updatedAt()

  @@index([user_id])
}

model LastSeenWord {
  id           String   @id() @default(uuid())
  last_seen_at DateTime @default(now())
  review_count Int      @default(0)
  user         User     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  user_id      String
  word_id      String

  @@unique([user_id, word_id])
  @@index([user_id])
}

model Paragraph {
  id                        String                   @id() @default(uuid())
  content                   String
  difficulty                Difficulty
  language                  Language
  length                    Length
  multiple_choice_exercises MultipleChoiceExercise[]

  @@index([language])
  @@index([difficulty])
}

model MultipleChoiceExercise {
  id           String    @id() @default(uuid())
  question     String
  options      String[]
  answer       Int // index of the correct answer in the options array
  explanation  String?
  paragraph_id String
  paragraph    Paragraph @relation(fields: [paragraph_id], references: [id])

  @@index([paragraph_id])
}

model Feedback {
  id         String   @id() @default(uuid())
  message    String
  user_id    String
  user       User     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  status     String   @default("pending")
  created_at DateTime @default(now())

  @@index([user_id])
}

model CollectionStats {
  id                             String     @id() @default(uuid())
  collection_id                  String
  collection                     Collection @relation(fields: [collection_id], references: [id], onDelete: Cascade)
  user_id                        String
  user                           User       @relation(fields: [user_id], references: [id], onDelete: Cascade)
  date                           DateTime   @db.Date
  words_reviewed_count           Int        @default(0)
  qa_practice_submissions        Int        @default(0)
  paragraph_practice_submissions Int        @default(0)
  created_at                     DateTime   @default(now())
  updated_at                     DateTime   @updatedAt()

  @@unique([collection_id, user_id, date])
  @@index([collection_id])
  @@index([user_id])
  @@index([date])
}

model AuditLog {
  id          String   @id() @default(uuid())
  action      String // Using String instead of enum for flexibility
  resource    String // e.g., "user", "collection", "word", "system"
  resource_id String? // ID of the affected resource
  user_id     String? // User who performed the action
  admin_id    String? // Admin who performed the action (if different from user)
  details     Json     @default("{}") // Additional details as JSON
  ip_address  String? // IP address of the request
  user_agent  String? // User agent string
  timestamp   DateTime @default(now())

  // Relations
  user  User? @relation("AuditLogUser", fields: [user_id], references: [id], onDelete: SetNull)
  admin User? @relation("AuditLogAdmin", fields: [admin_id], references: [id], onDelete: SetNull)

  @@index([action])
  @@index([resource])
  @@index([user_id])
  @@index([admin_id])
  @@index([timestamp])
  @@index([resource, resource_id])
}

model SeoSettings {
  id          String @id @default(uuid())
  title       String @default("Vocab - Learn Vocabulary with AI")
  description String @default("Learn new vocabulary with AI assistance. Improve your English and Vietnamese vocabulary through spaced repetition and AI-generated content.")
  keywords    String @default("vocabulary, learning, AI, English, Vietnamese, education, spaced repetition")

  og_title         String? // Open Graph title (defaults to title if null)
  og_description   String? // Open Graph description (defaults to description if null)
  og_image_url     String? // Open Graph image URL
  og_type          String   @default("website")
  twitter_card     String   @default("summary_large_image")
  twitter_site     String? // Twitter site handle
  twitter_creator  String? // Twitter creator handle
  canonical_url    String? // Canonical URL
  robots           String   @default("index, follow")
  language         String   @default("en")
  theme_color      String   @default("#000000")
  background_color String   @default("#ffffff")
  created_at       DateTime @default(now())
  updated_at       DateTime @updatedAt

  @@map("seo_settings")
}

model WordPackage {
  id              String     @id @default(uuid())
  name            String // Package name (e.g., "Business English", "Travel Vocabulary")
  description     String // Package description
  source_language Language // Language of the words being learned
  target_language Language // Language for explanations/translations
  difficulty      Difficulty // Package difficulty level
  category        String // Category (e.g., "Business", "Travel", "Academic")
  tags            String[] // Tags for filtering
  word_count      Int // Number of words in package
  is_active       Boolean    @default(true) // Whether package is available
  created_at      DateTime   @default(now())
  updated_at      DateTime   @updatedAt

  // Relations
  words           WordPackageWord[] // Words in this package
  user_selections UserWordPackage[] // Users who have selected this package

  @@map("word_packages")
}

model WordPackageWord {
  id              String   @id @default(uuid())
  word_package_id String
  term            String // The word/term
  language        Language // Language of the term
  created_at      DateTime @default(now())

  // Relations
  word_package WordPackage @relation(fields: [word_package_id], references: [id], onDelete: Cascade)

  @@unique([word_package_id, term, language])
  @@map("word_package_words")
}

model UserWordPackage {
  id              String   @id @default(uuid())
  user_id         String
  word_package_id String
  selected_at     DateTime @default(now())

  // Relations
  user         User        @relation(fields: [user_id], references: [id], onDelete: Cascade)
  word_package WordPackage @relation(fields: [word_package_id], references: [id], onDelete: Cascade)

  @@unique([user_id, word_package_id])
  @@map("user_word_packages")
}

model CefrWord {
  id         String   @id @default(uuid())
  term       String
  level      String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@unique([term])
  @@index([term])
  @@index([level])
}
