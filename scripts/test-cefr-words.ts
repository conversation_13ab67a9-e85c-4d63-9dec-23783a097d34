import { PrismaClient } from '@prisma/client';
import { CefrLevel } from '../src/models/cefr-word';

const prisma = new PrismaClient();

// Sample CEFR words for testing
const sampleWords = [
  // A1 Level
  { term: 'hello', level: CefrLevel.A1 },
  { term: 'goodbye', level: CefrLevel.A1 },
  { term: 'yes', level: CefrLevel.A1 },
  { term: 'no', level: CefrLevel.A1 },
  { term: 'please', level: CefrLevel.A1 },
  { term: 'thank', level: CefrLevel.A1 },
  { term: 'water', level: CefrLevel.A1 },
  { term: 'food', level: CefrLevel.A1 },
  { term: 'house', level: CefrLevel.A1 },
  { term: 'car', level: CefrLevel.A1 },

  // A2 Level
  { term: 'beautiful', level: CefrLevel.A2 },
  { term: 'important', level: CefrLevel.A2 },
  { term: 'different', level: CefrLevel.A2 },
  { term: 'interesting', level: CefrLevel.A2 },
  { term: 'difficult', level: CefrLevel.A2 },
  { term: 'expensive', level: CefrLevel.A2 },
  { term: 'comfortable', level: CefrLevel.A2 },
  { term: 'dangerous', level: CefrLevel.A2 },
  { term: 'popular', level: CefrLevel.A2 },
  { term: 'successful', level: CefrLevel.A2 },

  // B1 Level
  { term: 'achievement', level: CefrLevel.B1 },
  { term: 'advantage', level: CefrLevel.B1 },
  { term: 'atmosphere', level: CefrLevel.B1 },
  { term: 'attitude', level: CefrLevel.B1 },
  { term: 'background', level: CefrLevel.B1 },
  { term: 'behavior', level: CefrLevel.B1 },
  { term: 'challenge', level: CefrLevel.B1 },
  { term: 'character', level: CefrLevel.B1 },
  { term: 'community', level: CefrLevel.B1 },
  { term: 'confidence', level: CefrLevel.B1 },

  // B2 Level
  { term: 'accommodate', level: CefrLevel.B2 },
  { term: 'acknowledge', level: CefrLevel.B2 },
  { term: 'anticipate', level: CefrLevel.B2 },
  { term: 'appreciate', level: CefrLevel.B2 },
  { term: 'comprehensive', level: CefrLevel.B2 },
  { term: 'consequence', level: CefrLevel.B2 },
  { term: 'demonstrate', level: CefrLevel.B2 },
  { term: 'distinguish', level: CefrLevel.B2 },
  { term: 'emphasize', level: CefrLevel.B2 },
  { term: 'equivalent', level: CefrLevel.B2 },

  // C1 Level
  { term: 'accumulate', level: CefrLevel.C1 },
  { term: 'ambiguous', level: CefrLevel.C1 },
  { term: 'arbitrary', level: CefrLevel.C1 },
  { term: 'coherent', level: CefrLevel.C1 },
  { term: 'compatible', level: CefrLevel.C1 },
  { term: 'conceive', level: CefrLevel.C1 },
  { term: 'constitute', level: CefrLevel.C1 },
  { term: 'contemporary', level: CefrLevel.C1 },
  { term: 'controversy', level: CefrLevel.C1 },
  { term: 'criteria', level: CefrLevel.C1 },

  // C2 Level
  { term: 'aberration', level: CefrLevel.C2 },
  { term: 'accentuate', level: CefrLevel.C2 },
  { term: 'acquiesce', level: CefrLevel.C2 },
  { term: 'amalgamate', level: CefrLevel.C2 },
  { term: 'articulate', level: CefrLevel.C2 },
  { term: 'assimilate', level: CefrLevel.C2 },
  { term: 'circumvent', level: CefrLevel.C2 },
  { term: 'corroborate', level: CefrLevel.C2 },
  { term: 'delineate', level: CefrLevel.C2 },
  { term: 'elucidate', level: CefrLevel.C2 },
];

async function seedTestCefrWords() {
  console.log('🌱 Seeding test CEFR words...');

  try {
    // Clear existing test data (optional)
    console.log('🧹 Clearing existing test data...');
    await prisma.cefrWord.deleteMany({
      where: {
        term: {
          in: sampleWords.map(word => word.term),
        },
      },
    });

    // Insert sample words
    console.log('📝 Inserting sample CEFR words...');
    const result = await prisma.cefrWord.createMany({
      data: sampleWords,
      skipDuplicates: true,
    });

    console.log(`✅ Successfully seeded ${result.count} CEFR words`);

    // Display statistics
    const stats = await prisma.cefrWord.groupBy({
      by: ['level'],
      _count: {
        id: true,
      },
      orderBy: {
        level: 'asc',
      },
    });

    console.log('\n📊 CEFR Words Statistics:');
    stats.forEach(stat => {
      console.log(`  ${stat.level}: ${stat._count.id} words`);
    });

    const totalWords = await prisma.cefrWord.count();
    console.log(`\n📈 Total CEFR words in database: ${totalWords}`);

  } catch (error) {
    console.error('❌ Error seeding CEFR words:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function testCefrWordsAPI() {
  console.log('\n🧪 Testing CEFR Words functionality...');

  try {
    // Test basic operations
    console.log('Testing basic CRUD operations...');
    
    // Create a test word
    const testWord = await prisma.cefrWord.create({
      data: {
        term: 'test-word-' + Date.now(),
        level: CefrLevel.A1,
      },
    });
    console.log(`✅ Created test word: ${testWord.term}`);

    // Find the word
    const foundWord = await prisma.cefrWord.findUnique({
      where: { id: testWord.id },
    });
    console.log(`✅ Found word: ${foundWord?.term}`);

    // Search words
    const searchResults = await prisma.cefrWord.findMany({
      where: {
        term: {
          contains: 'hello',
          mode: 'insensitive',
        },
      },
      take: 5,
    });
    console.log(`✅ Search results: ${searchResults.length} words found`);

    // Get words by level
    const a1Words = await prisma.cefrWord.findMany({
      where: { level: CefrLevel.A1 },
      take: 5,
    });
    console.log(`✅ A1 words: ${a1Words.length} words found`);

    // Get level statistics
    const levelStats = await prisma.cefrWord.groupBy({
      by: ['level'],
      _count: { id: true },
    });
    console.log(`✅ Level statistics: ${levelStats.length} levels`);

    // Clean up test word
    await prisma.cefrWord.delete({
      where: { id: testWord.id },
    });
    console.log(`✅ Cleaned up test word`);

    console.log('\n🎉 All tests passed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

async function main() {
  console.log('🚀 Starting CEFR Words test script...\n');

  await seedTestCefrWords();
  await testCefrWordsAPI();

  console.log('\n✨ CEFR Words test script completed successfully!');
}

// Run the script
if (require.main === module) {
  main()
    .catch((error) => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}

export { seedTestCefrWords, testCefrWordsAPI };
